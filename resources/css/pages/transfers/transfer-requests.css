/**
 * CSS tùy chỉnh cho trang danh sách yêu cầu chuyển kho
 * <PERSON>hắc phục vấn đề hiển thị dropdown trong cột thao tác
 */

/* Cột thao tác - cố định width và căn giữa */
.datatables-transfer-requests .actions-column {
    width: 120px !important;
    min-width: 120px !important;
    max-width: 120px !important;
    text-align: center !important;
    vertical-align: middle !important;
}

/* Dropdown trong DataTable */
.datatables-transfer-requests .dropdown {
    position: relative;
    display: inline-block;
}

/* Nút dropdown */
.datatables-transfer-requests .dropdown-toggle {
    border: none;
    background: transparent;
    padding: 0.375rem 0.5rem;
    font-size: 1.125rem;
    line-height: 1;
    color: #697a8d;
    cursor: pointer;
    transition: color 0.15s ease-in-out;
}

.datatables-transfer-requests .dropdown-toggle:hover {
    color: #5a6c7d;
}

.datatables-transfer-requests .dropdown-toggle:focus {
    outline: none;
    box-shadow: none;
}

/* Dropdown menu */
.datatables-transfer-requests .dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1050;
    min-width: 160px;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 0.875rem;
    color: #697a8d;
    text-align: left;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #d9dee3;
    border-radius: 0.375rem;
    box-shadow: 0 0.25rem 1rem rgba(161, 172, 184, 0.45);
}

/* Dropdown items */
.datatables-transfer-requests .dropdown-item {
    display: block;
    width: 100%;
    padding: 0.5rem 1rem;
    clear: both;
    font-weight: 400;
    color: #697a8d;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;
}

.datatables-transfer-requests .dropdown-item:hover,
.datatables-transfer-requests .dropdown-item:focus {
    color: #5a6c7d;
    background-color: #f8f9fa;
}

.datatables-transfer-requests .dropdown-item.active,
.datatables-transfer-requests .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #696cff;
}

.datatables-transfer-requests .dropdown-item.disabled,
.datatables-transfer-requests .dropdown-item:disabled {
    color: #c7cdd4;
    pointer-events: none;
    background-color: transparent;
}

/* Icon trong dropdown item */
.datatables-transfer-requests .dropdown-item i {
    margin-right: 0.5rem;
    font-size: 0.875rem;
}

/* Khắc phục overflow cho table responsive */
.card-datatable {
    overflow: visible !important;
}

.card-datatable .table-responsive {
    overflow-x: auto;
    overflow-y: visible;
}

/* Đảm bảo dropdown không bị cắt */
.datatables-transfer-requests tbody tr {
    position: relative;
}

.datatables-transfer-requests .dropdown-menu {
    transform: translateX(-50%);
    left: 50% !important;
    right: auto !important;
}

/* Dropdown menu end alignment */
.datatables-transfer-requests .dropdown-menu.dropdown-menu-end {
    transform: translateX(-80%);
    left: 80% !important;
    right: auto !important;
}

/* Responsive design cho mobile */
@media (max-width: 768px) {
    .datatables-transfer-requests .actions-column {
        width: 80px !important;
        min-width: 80px !important;
        max-width: 80px !important;
    }

    .datatables-transfer-requests .dropdown-menu {
        min-width: 140px;
        transform: translateX(-80%);
    }

    .datatables-transfer-requests .dropdown-menu.dropdown-menu-end {
        transform: translateX(-90%);
    }

    .datatables-transfer-requests .dropdown-item {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .datatables-transfer-requests .actions-column {
        width: 60px !important;
        min-width: 60px !important;
        max-width: 60px !important;
    }

    .datatables-transfer-requests .dropdown-toggle {
        padding: 0.25rem;
        font-size: 1rem;
    }

    .datatables-transfer-requests .dropdown-menu {
        min-width: 120px;
        transform: translateX(-90%);
    }

    .datatables-transfer-requests .dropdown-menu.dropdown-menu-end {
        transform: translateX(-95%);
    }
}

/* Dark mode support */
.dark-style .datatables-transfer-requests .dropdown-toggle {
    color: #a7acb2;
}

.dark-style .datatables-transfer-requests .dropdown-toggle:hover {
    color: #c2c6cc;
}

.dark-style .datatables-transfer-requests .dropdown-menu {
    background-color: #2f3349;
    border-color: #444564;
    box-shadow: 0 0.25rem 1rem rgba(20, 20, 29, 0.45);
}

.dark-style .datatables-transfer-requests .dropdown-item {
    color: #a7acb2;
}

.dark-style .datatables-transfer-requests .dropdown-item:hover,
.dark-style .datatables-transfer-requests .dropdown-item:focus {
    color: #c2c6cc;
    background-color: #383b53;
}

.dark-style .datatables-transfer-requests .dropdown-item.active,
.dark-style .datatables-transfer-requests .dropdown-item:active {
    background-color: #696cff;
}

.dark-style .datatables-transfer-requests .dropdown-item.disabled,
.dark-style .datatables-transfer-requests .dropdown-item:disabled {
    color: #5e6692;
}

/* Cải thiện hiển thị trên các màn hình lớn */
@media (min-width: 1200px) {
    .datatables-transfer-requests .actions-column {
        width: 140px !important;
        min-width: 140px !important;
        max-width: 140px !important;
    }

    .datatables-transfer-requests .dropdown-menu {
        min-width: 180px;
    }
}

/* Đảm bảo z-index đúng cho dropdown */
.datatables-transfer-requests .dropdown.show .dropdown-menu {
    display: block;
    z-index: 1055;
}

/* Animation cho dropdown */
.datatables-transfer-requests .dropdown-menu {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
    transition: opacity 0.15s ease, transform 0.15s ease;
    pointer-events: none;
}

.datatables-transfer-requests .dropdown-menu.dropdown-menu-end {
    transform: translateX(-80%) translateY(-10px);
}

.datatables-transfer-requests .dropdown.show .dropdown-menu {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
    pointer-events: auto;
}

.datatables-transfer-requests .dropdown.show .dropdown-menu.dropdown-menu-end {
    transform: translateX(-80%) translateY(0);
}
